# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "b1860dcefe329404ac254d54824500fe"
application_url = "https://story-pg-indian-principle.trycloudflare.com"
embedded = true
name = "search-engine"
handle = "search-engine-2"

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true

[webhooks]
api_version = "2025-07"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_products"

[auth]
redirect_urls = ["https://story-pg-indian-principle.trycloudflare.com/auth/callback", "https://story-pg-indian-principle.trycloudflare.com/auth/shopify/callback", "https://story-pg-indian-principle.trycloudflare.com/api/auth/callback"]

[pos]
embedded = false

[app_proxy]
url = "https://story-pg-indian-principle.trycloudflare.com/api/search/settings"
subpath = "dinosearch"
prefix = "apps"
