import type { SearchSettings } from "./settings.server";


/**
 * Sync search settings to Shopify metafields
 * This ensures the frontend JavaScript can access the current settings
 *
 * Note: This is a simplified approach. In production, you would need
 * to use the Shopify Admin API with proper authentication.
 * For now, we'll store the settings in a way that can be accessed by the theme.
 */
export async function syncSettingsToMetafields(
  shop: string,
  settings: SearchSettings
): Promise<void> {
  try {
    // For now, we'll just log that we would sync to metafields
    // In a full implementation, you would use the Shopify Admin API
    console.log(`Would sync settings to metafields for shop: ${shop}`, {
      enabled: settings.isEnabled,
      apiEndpoint: '/api/search',
      resultsPerPage: settings.apiPageSize,
      // UI Configuration Parameters
      gapBetweenSearchBarAndModal: settings.gapBetweenSearchBarAndModal,
      maxSuggestions: settings.maxSuggestions,
      debounceDelay: settings.debounceDelay,
      imageSize: settings.imageSize,
      minQueryLength: settings.minQueryLength,
      language: settings.language,
      currency: settings.currency
    });

    // TODO: Implement actual metafield syncing using Shopify Admin API
    // This would require proper session management and GraphQL mutations

    console.log(`Successfully synced search settings to metafields for shop: ${shop}`);
  } catch (error) {
    console.error(`Failed to sync settings to metafields for shop ${shop}:`, error);
    // Don't throw - this is not critical for app functionality
  }
}

/**
 * Remove search engine metafields (cleanup when app is uninstalled)
 */
export async function removeSettingsMetafields(shop: string): Promise<void> {
  try {
    // TODO: Implement metafield cleanup
    console.log(`Would remove search engine metafields for shop: ${shop}`);
  } catch (error) {
    console.error(`Failed to remove metafields for shop ${shop}:`, error);
  }
}
