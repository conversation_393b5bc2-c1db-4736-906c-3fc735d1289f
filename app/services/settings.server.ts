import { PrismaClient } from "@prisma/client";
import { syncSettingsToMetafields } from "./metafields.server";

const prisma = new PrismaClient();

export interface Settings {
  id: string;
  shop: string;
  fastApiProxyUrl: string;
  fastApiApiKey: string | null;
  websiteKey: string;
  elasticsearchIndex: string;
  isEnabled: boolean;
  gapBetweenSearchBarAndModal: number;
  maxSuggestions: number;
  debounceDelay: number;
  imageSize: number;
  apiPageSize: number;
  minQueryLength: number;
  language: string;
  currency: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface UpdateSettingsData {
  fastApiProxyUrl?: string;
  fastApiApiKey?: string;
  websiteKey?: string;
  elasticsearchIndex?: string;
  isEnabled?: boolean;
  gapBetweenSearchBarAndModal?: number;
  maxSuggestions?: number;
  debounceDelay?: number;
  imageSize?: number;
  apiPageSize?: number;
  minQueryLength?: number;
  language?: string;
  currency?: string;
}

// Alias for compatibility
export type UpdateSearchSettingsData = UpdateSettingsData;
export type SearchSettings = Settings;

/**
 * Get search settings for a specific shop
 * Returns null if no settings exist - admin must configure them first
 */
export async function getSettings(shop: string): Promise<Settings | null> {
  const settings = await prisma.searchSettings.findUnique({
    where: { shop }
  });

  return settings;
}

/**
 * Update search settings for a specific shop
 */
export async function updateSettings(
  shop: string,
  data: UpdateSettingsData
): Promise<Settings> {
  // Ensure settings exist first
  await getSettings(shop);

  const updatedSettings = await prisma.searchSettings.update({
    where: { shop },
    data: {
      ...data,
      updatedAt: new Date(),
    }
  });

  // Sync settings to Shopify metafields for frontend access
  await syncSettingsToMetafields(shop, updatedSettings);

  return updatedSettings;
}

/**
 * Test connection to Shopify FastAPI service with given settings
 */
export async function testFastApiConnection(
  fastApiProxyUrl: string,
  fastApiApiKey: string | null
): Promise<{ success: boolean; error?: string; responseTime?: number }> {
  try {
    const startTime = Date.now();

    const response = await fetch(`${fastApiProxyUrl}/shopify/health`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        ...(fastApiApiKey && { "X-API-Key": fastApiApiKey }),
      },
      signal: AbortSignal.timeout(5000), // 5 second timeout
    });

    const responseTime = Date.now() - startTime;

    if (!response.ok) {
      return {
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}`,
        responseTime,
      };
    }

    return {
      success: true,
      responseTime,
    };
  } catch (error) {
    console.log(error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Connection failed",
    };
  }
}

/**
 * Test Shopify Elasticsearch index accessibility
 */
export async function testElasticsearchIndex(
  fastApiProxyUrl: string,
  fastApiApiKey: string | null,
  websiteKey: string,
  elasticsearchIndex: string
): Promise<{ success: boolean; error?: string; documentCount?: number }> {
  try {
    const response = await fetch(`${fastApiProxyUrl}/shopify/index/status`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...(fastApiApiKey && { "X-API-Key": fastApiApiKey }),
      },
      body: JSON.stringify({
        index_name: elasticsearchIndex,
        website_key: websiteKey,
      }),
      signal: AbortSignal.timeout(10000), // 10 second timeout
    });

    if (!response.ok) {
      return {
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}`,
      };
    }

    const data = await response.json();
    console.log(JSON.stringify(data, null, 2))

    return {
      success: data.success || false,
      documentCount: data.document_count || 0,
      error: data.error || undefined,
    };
  } catch (error) {
    console.log(error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Index check failed",
    };
  }
}

/**
 * Validate search settings data
 */
export function validateSearchSettings(data: UpdateSettingsData): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!data.fastApiProxyUrl || typeof data.fastApiProxyUrl !== 'string') {
    errors.push('FastAPI Proxy URL is required');
  } else {
    try {
      new URL(data.fastApiProxyUrl);
    } catch {
      errors.push('FastAPI Proxy URL must be a valid URL');
    }
  }

  if (!data.websiteKey || typeof data.websiteKey !== 'string') {
    errors.push('Website Key is required');
  }

  if (!data.elasticsearchIndex || typeof data.elasticsearchIndex !== 'string') {
    errors.push('Elasticsearch Index is required');
  }

  // New fields validation
  if (typeof data.gapBetweenSearchBarAndModal === 'number' && (data.gapBetweenSearchBarAndModal < 0 || data.gapBetweenSearchBarAndModal > 500)) {
    errors.push('Gap Between Search Bar and Modal must be between 0 and 500');
  }
  if (typeof data.maxSuggestions === 'number' && (data.maxSuggestions < 1 || data.maxSuggestions > 50)) {
    errors.push('Max Suggestions must be between 1 and 50');
  }
  if (typeof data.debounceDelay === 'number' && (data.debounceDelay < 0 || data.debounceDelay > 5000)) {
    errors.push('Debounce Delay must be between 0 and 5000 ms');
  }
  if (typeof data.imageSize === 'number' && (data.imageSize < 10 || data.imageSize > 500)) {
    errors.push('Image Size must be between 10 and 500 px');
  }
  if (typeof data.apiPageSize === 'number' && (data.apiPageSize < 1 || data.apiPageSize > 100)) {
    errors.push('API Page Size must be between 1 and 100');
  }
  if (typeof data.minQueryLength === 'number' && (data.minQueryLength < 1 || data.minQueryLength > 10)) {
    errors.push('Min Query Length must be between 1 and 10');
  }

  const allowedLanguages = ['de-DE', 'en-US', 'fr-FR', 'es-ES'];
  if (data.language && !allowedLanguages.includes(data.language)) {
    errors.push('Language must be one of: ' + allowedLanguages.join(', '));
  }
  const allowedCurrencies = ['EUR', 'USD', 'GBP', 'CHF'];
  if (data.currency && !allowedCurrencies.includes(data.currency)) {
    errors.push('Currency must be one of: ' + allowedCurrencies.join(', '));
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Aliases for compatibility with existing code
 */
export const getSearchSettings = getSettings;
export const updateSearchSettings = updateSettings;

/**
 * Get all shops with search settings (for admin purposes)
 */
export async function getAllSettings(): Promise<Settings[]> {
  return await prisma.searchSettings.findMany({
    orderBy: { updatedAt: 'desc' }
  });
}

/**
 * Delete search settings for a shop (cleanup when app is uninstalled)
 */
export async function deleteSettings(shop: string): Promise<void> {
  await prisma.searchSettings.delete({
    where: { shop }
  }).catch(() => {
    // Ignore errors if settings don't exist
  });
}

/**
 * Validate settings data
 */
export function validateSettings(data: UpdateSettingsData): { 
  isValid: boolean; 
  errors: string[] 
} {
  const errors: string[] = [];

  if (data.fastApiProxyUrl !== undefined) {
    try {
      new URL(data.fastApiProxyUrl);
    } catch {
      errors.push("FastAPI Proxy URL must be a valid URL");
    }
  }

  if (data.websiteKey !== undefined) {
    if (!data.websiteKey || data.websiteKey.trim().length === 0) {
      errors.push("Website Key cannot be empty");
    }
    if (data.websiteKey && !/^[a-zA-Z0-9_-]+$/.test(data.websiteKey)) {
      errors.push("Website Key can only contain letters, numbers, underscores, and hyphens");
    }
  }

  if (data.elasticsearchIndex !== undefined) {
    if (!data.elasticsearchIndex || data.elasticsearchIndex.trim().length === 0) {
      errors.push("Elasticsearch Index cannot be empty");
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Cleanup function to close Prisma connection
export async function cleanup() {
  await prisma.$disconnect();
}
