import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { getSettings, type Settings } from "../services/settings.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    const { session } = await authenticate.public.appProxy(request);

    if (!session) {
      return json({ error: "Authentication failed" }, { status: 401 });
    }

    const { shop } = session;
    const settings: Settings | null = await getSettings(shop);

    if (!settings) {
      return json({ enable_search_engine: false });
    }

    const responseSettings = {
      fastapi_proxy_url: settings.fastApiProxyUrl || "",
      api_key: settings.fastApiApiKey || "",
      website_key: settings.websiteKey || "",
      elasticsearch_index_name: settings.elasticsearchIndex || "",
      enable_search_engine: settings.isEnabled ?? false,
      gap_between_search_bar_and_modal: settings.gapBetweenSearchBarAndModal ?? 1,
      max_suggestions: settings.maxSuggestions ?? 5,
      debounce_delay: settings.debounceDelay ?? 300,
      image_size: settings.imageSize ?? 50,
      api_page_size: settings.apiPageSize ?? 10,
      min_query_length: settings.minQueryLength ?? 2,
      language: settings.language ?? 'de-DE',
      currency: settings.currency ?? 'EUR',
    };

    return json(responseSettings);
  } catch (error) {
    console.error("[App Proxy] Error processing request:", error);
    return json({ error: "Failed to load settings", enable_search_engine: false }, { status: 500 });
  }
};