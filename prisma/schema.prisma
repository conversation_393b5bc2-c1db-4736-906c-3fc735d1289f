// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

// Note that some adapters may set a maximum length for the String type by default, please ensure your strings are long
// enough when changing adapters.
// See https://www.prisma.io/docs/orm/reference/prisma-schema-reference#string for more information
datasource db {
  provider = "sqlite"
  url      = "file:dev.sqlite"
}

model Session {
  id            String    @id
  shop          String
  state         String
  isOnline      <PERSON>an   @default(false)
  scope         String?
  expires       DateTime?
  accessToken   String
  userId        BigInt?
  firstName     String?
  lastName      String?
  email         String?
  accountOwner  Boolean   @default(false)
  locale        String?
  collaborator  <PERSON><PERSON><PERSON>?  @default(false)
  emailVerified Boolean?  @default(false)
}

model SearchSettings {
  id                    String   @id @default(cuid())
  shop                  String   @unique
  fastApiProxyUrl       String   @default("http://localhost:8000")
  fastApiApiKey         String?
  websiteKey            String   @unique
  elasticsearchIndex    String   @default("shopify_products")
  isEnabled             Boolean  @default(true)
  
  /* UI Configuration Parameters */
  gapBetweenSearchBarAndModal Int      @default(1)
  maxSuggestions             Int      @default(5)
  debounceDelay              Int      @default(300)
  imageSize                  Int      @default(50)
  apiPageSize                Int      @default(10)
  minQueryLength             Int      @default(2)
  language                   String   @default("de-DE")
  currency                   String   @default("EUR")
  
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
}
