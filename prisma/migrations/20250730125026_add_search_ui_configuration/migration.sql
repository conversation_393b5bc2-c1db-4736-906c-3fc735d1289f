-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_SearchSettings" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "shop" TEXT NOT NULL,
    "fastApiProxyUrl" TEXT NOT NULL DEFAULT 'http://localhost:8000',
    "fastApiApiKey" TEXT,
    "websiteKey" TEXT NOT NULL,
    "elasticsearchIndex" TEXT NOT NULL DEFAULT 'shopify_products',
    "isEnabled" BOOLEAN NOT NULL DEFAULT true,
    "gapBetweenSearchBarAndModal" INTEGER NOT NULL DEFAULT 1,
    "maxSuggestions" INTEGER NOT NULL DEFAULT 5,
    "debounceDelay" INTEGER NOT NULL DEFAULT 300,
    "imageSize" INTEGER NOT NULL DEFAULT 50,
    "apiPageSize" INTEGER NOT NULL DEFAULT 10,
    "minQueryLength" INTEGER NOT NULL DEFAULT 2,
    "language" TEXT NOT NULL DEFAULT 'de-DE',
    "currency" TEXT NOT NULL DEFAULT 'EUR',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);
INSERT INTO "new_SearchSettings" ("createdAt", "elasticsearchIndex", "fastApiApiKey", "fastApiProxyUrl", "id", "isEnabled", "shop", "updatedAt", "websiteKey") SELECT "createdAt", "elasticsearchIndex", "fastApiApiKey", "fastApiProxyUrl", "id", "isEnabled", "shop", "updatedAt", "websiteKey" FROM "SearchSettings";
DROP TABLE "SearchSettings";
ALTER TABLE "new_SearchSettings" RENAME TO "SearchSettings";
CREATE UNIQUE INDEX "SearchSettings_shop_key" ON "SearchSettings"("shop");
CREATE UNIQUE INDEX "SearchSettings_websiteKey_key" ON "SearchSettings"("websiteKey");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
