/* Custom Search Suggestions Container */
.custom-search-suggestions {
  position: fixed;
  top: 0;
  left: 0;
  width: auto;
  max-width: none;
  background-color: #fff;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  z-index: 1000;
  padding: 10px;
  display: none;
}

/* Suggestions Header */
.suggestions-header {
  margin-bottom: 10px;
}

.search-results-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
}

/* Suggestions List */
.suggestions-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

/* Desktop layout - main structure */
.suggestions-list li {
  padding: 8px 0;
  border-bottom: 1px solid #ddd;
}

.suggestions-list li:last-child {
  border-bottom: none;
}

/* Suggestion link container - critical for layout */
.suggestion-link {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 8px 0;
  text-decoration: none;
  color: inherit;
}

/* Left section - image and product info */
.suggestion-left {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1 1 auto;
}

/* Ensure product info doesn't collapse */
.product-info {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  min-width: 0; /* Fix for flexbox collapsing with long text */
}

.suggestion-image {
  width: 50px;
  height: 50px;
  object-fit: cover;
  object-position: center;
  margin-right: 10px;
}

/* Truncate vendor names to prevent pushing titles */
.vendor {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px; /* Adjust based on your design */
}

.suggestion-title {
  font-size: 14px;
  margin: 0;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.3;
  text-decoration: none; /* Ensure no underline by default */
  transition: text-decoration 0.2s ease; /* Smooth transition */
}

/* Add underline to title ONLY when hovering over the suggestion */
.suggestion-link:hover .product-info h4 {
  text-decoration: underline;
}

.suggestion-link:focus-within .suggestion-title {
  text-decoration: underline;
  background-color: #f8f8f8;
  border-radius: 4px;
}

/* Price section */
.suggestion-right {
  white-space: nowrap;
  display: flex;
  justify-content: flex-end;
}

.price {
  font-weight: bold;
  color: #333;
}

/* Show All Results Button */
.show-all-results-button {
  display: block;
  width: 100%;
  padding: 10px;
  background-color: transparent;
  border: none;
  text-align: center;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.show-all-results-button:hover {
  background-color: #f0f0f0;
}

/* Mobile-specific adjustments */
@media screen and (max-width: 768px) {
  .suggestion-link {
    flex-direction: row;
    gap: 10px;
  }
  
  .suggestion-left {
    flex: 0 0 50px;
  }
  
  .product-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .suggestion-right {
    display: flex;
    justify-content: flex-end;
    margin-top: 4px;
  }
  
  .suggestions-list img {
    width: 50px !important;
    height: 50px !important;
  }
  
  .price {
    font-size: 13px;
  }
  
  .vendor {
    font-size: 12px;
    margin-bottom: 2px;
  }
  
  .product-info h4 {
    font-size: 13px;
  }
}