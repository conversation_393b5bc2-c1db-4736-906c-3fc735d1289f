
/**
 * Manages all API communication for the search functionality
 */
class SearchAPI {
  /**
   * @param {string} appProxyPath - Path to the app proxy endpoint
   * @param {Object} config - Configuration object with default values
   */
  constructor(appProxyPath = '/apps/dinosearch', config = {}) {
    this.appProxyPath = appProxyPath;
    this.settings = null;
    
    // Default configuration with override capability
    this.config = {
      apiPageSize: 10,               // Default page size for API
      ...config                     // Allow overrides from settings
    };
  }

  /**
   * Fetches configuration settings from the app proxy
   * @returns {Promise<Object>} Settings object
   */
  async fetchSettings() {
    try {
      const response = await fetch(this.appProxyPath, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch settings (${response.status}): ${errorText}`);
      }
      
      const settings = await response.json();
      this.settings = settings;
      return settings;
    } catch (error) {
      console.error('[SearchAPI] Error fetching settings:', error);
      return { enable_search_engine: false };
    }
  }
  
  /**
   * Fetches search suggestions from the API
   * @param {string} query - Search query
   * @param {Object} settings - Configuration settings
   * @returns {Promise<Array>} Array of suggestion items
   */
  async fetchSuggestions(query, settings) {
    if (!settings.enable_search_engine) {
      return [];
    }
    
    const suggestionsEndpoint = `${settings.fastapi_proxy_url}/shopify/search`;
    
    const requestBody = {
      index: settings.elasticsearch_index_name,
      pattern: query,
      current_page: 0,
      page_size: this.config.apiPageSize,
      args: {}
    };
    
    try {
      const response = await fetch(suggestionsEndpoint, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${settings.api_key}`,
          'X-Website-Key': settings.website_key,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`[SearchAPI] Suggestions API error (${response.status}):`, errorText);
        return [];
      }
      
      const data = await response.json();
      let suggestionsArray = [];
      
      if (data && Array.isArray(data.data)) {
        suggestionsArray = data.data;
      } else if (data && typeof data === 'object' && Array.isArray(data.results)) {
        suggestionsArray = data.results;
      } else {
        console.warn('[SearchAPI] Unexpected suggestions response format:', data);
        suggestionsArray = [];
      }
      
      return suggestionsArray;
    } catch (error) {
      console.error('[SearchAPI] Network error fetching suggestions:', error);
      return [];
    }
  }
}

/**
 * Manages rendering of search suggestions UI
 */
class SearchRenderer {
  /**
   * @param {HTMLElement} suggestionsContainer - Container for suggestions
   * @param {Object} config - Configuration object with default values
   */
  constructor(suggestionsContainer, config = {}) {
    this.suggestionsContainer = suggestionsContainer;
    
    // Default configuration with override capability
    this.config = {
      maxSuggestions: 5,             // Default max suggestions
      minQueryLength: 2,             // Default min query length
      language: 'de-DE',             // Default language
      currency: 'EUR',               // Default currency
      ...config                     // Allow overrides from settings
    };
    
    this.DEFAULT_SUGGESTION_SELECTORS = [
      '.predictive-search',
      '[data-predictive-search]',
      '.search__results',
      '.search-results',
      'ul.search-suggestions',
      'div[role="listbox"]',
      '.search-bar__results',
    ];
  }

  /**
   * Hides default Shopify suggestions
   */
  hideDefaultSuggestions() {
    this.DEFAULT_SUGGESTION_SELECTORS.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        element.style.display = 'none';
        element.style.visibility = 'hidden';
      });
    });
  }

  /**
   * Renders search suggestions in the UI
   * @param {Array} suggestions - Array of suggestion items
   * @param {string} query - Current search query
   * @returns {Object} References to rendered elements
   */
  renderSuggestions(suggestions, query) {
    if (!this.suggestionsContainer) return null;
    
    // Clear previous content
    this.suggestionsContainer.innerHTML = '';
    if (!Array.isArray(suggestions) || suggestions.length === 0) {
      this.suggestionsContainer.style.display = 'none';
      return null;
    }
    
    // Header
    const header = document.createElement('div');
    header.className = 'suggestions-header';
    header.innerHTML = `
      <h3 class="search-results-title">
        Suchergebnisse für "<span class="search-query-highlight">${query}</span>"
      </h3>
    `;
    this.suggestionsContainer.appendChild(header);
    
    // List
    const ul = document.createElement('ul');
    ul.id = 'suggestions-list';
    ul.className = 'suggestions-list';
    
    suggestions.slice(0, this.config.maxSuggestions).forEach(item => {
      const li = document.createElement('li');
      
      // Link container
      const link = document.createElement('a');
      link.href = item.url || '#';
      link.className = 'suggestion-link';
      
      // Image
      const img = document.createElement('img');
      img.src = item.primary_image || '';
      img.alt = item.title || '';
      img.className = 'suggestion-image';
      img.width = 50;
      img.height = 50;
      
      // Product info
      const info = document.createElement('div');
      info.className = 'product-info';
      
      // Vendor
      const vendor = document.createElement('div');
      vendor.className = 'vendor';
      vendor.textContent = item.vendor || 'Unbekannt';
      info.appendChild(vendor);
      
      // Title
      const title = document.createElement('h4');
      title.className = 'suggestion-title';
      title.textContent = item.title || 'Produkt';
      info.appendChild(title);
      
      // Price
      const price = document.createElement('span');
      price.className = 'price';
      price.textContent = typeof item.price === 'number'
        ? new Intl.NumberFormat(this.config.language, { style: 'currency', currency: this.config.currency }).format(item.price)
        : `${item.price} €`;
      
      // Assemble components
      link.appendChild(img);
      link.appendChild(info);
      link.appendChild(price);
      li.appendChild(link);
      ul.appendChild(li);
    });
    
    this.suggestionsContainer.appendChild(ul);
    
    // Footer
    const footer = document.createElement('div');
    footer.className = 'suggestions-footer';
    
    const btn = document.createElement('button');
    btn.type = 'button';
    btn.className = 'show-all-results-button';
    btn.textContent = 'Alle Ergebnisse anzeigen';
    
    footer.appendChild(btn);
    this.suggestionsContainer.appendChild(footer);
    
    // Show panel
    this.suggestionsContainer.style.display = 'block';
    
    return {
      showAllButton: btn,
      suggestionsList: ul
    };
  }

  /**
   * Clears all suggestions from the UI
   */
  clearSuggestions() {
    if (this.suggestionsContainer) {
      this.suggestionsContainer.innerHTML = '';
      this.suggestionsContainer.style.display = 'none';
    }
    this.hideDefaultSuggestions();
  }
}

/**
 * Manages positioning of the suggestions container
 */
class SearchPositioning {
  /**
   * @param {HTMLElement} searchInput - Search input element
   * @param {HTMLElement} suggestionsContainer - Container for suggestions
   * @param {Object} config - Configuration object with default values
   */
  constructor(searchInput, suggestionsContainer, config = {}) {
    this.searchInput = searchInput;
    this.suggestionsContainer = suggestionsContainer;
    
    // Default configuration with override capability
    this.config = {
      gapBetweenSearchBarAndModal: 1,  // Default 1px gap
      imageSize: 50,                   // Default image size
      ...config                        // Allow overrides from settings
    };
    
    this.resizeHandler = null;
  }
  
  /**
   * Positions the suggestions container below the search input
   */
  positionContainer() {
    if (!this.searchInput || !this.suggestionsContainer) return;
    const inputRect = this.searchInput.getBoundingClientRect();
    this.suggestionsContainer.style.width = `${inputRect.width}px`;
    this.suggestionsContainer.style.top = `${inputRect.bottom + window.scrollY + this.config.gapBetweenSearchBarAndModal}px`;
    this.suggestionsContainer.style.left = `${inputRect.left + window.scrollX}px`;
    this.suggestionsContainer.style.position = 'fixed';
    this.suggestionsContainer.style.zIndex = '1000';
  }

  /**
   * Sets up resize handler to maintain positioning
   * @returns {Function} Resize handler function
   */
  setupResizeHandler() {
    this.resizeHandler = () => {
      if (this.suggestionsContainer && this.suggestionsContainer.style.display === 'block') {
        this.positionContainer();
      }
    };
    
    window.addEventListener('resize', this.resizeHandler);
    return this.resizeHandler;
  }

  /**
   * Cleans up the resize handler
   */
  cleanupResizeHandler() {
    if (this.resizeHandler) {
      window.removeEventListener('resize', this.resizeHandler);
      this.resizeHandler = null;
    }
  }
}

/**
 * Manages event binding and handling for the search functionality
 */
class SearchEventManager {
  /**
   * @param {HTMLElement} searchForm - Search form element
   * @param {HTMLElement} searchInput - Search input element
   * @param {HTMLElement} suggestionsContainer - Container for suggestions
   * @param {SearchAPI} api - API manager instance
   * @param {SearchRenderer} renderer - Renderer instance
   * @param {SearchPositioning} positioning - Positioning manager instance
   * @param {Object} config - Configuration object with default values
   */
  constructor(searchForm, searchInput, suggestionsContainer, api, renderer, positioning, config = {}) {
    this.searchForm = searchForm;
    this.searchInput = searchInput;
    this.suggestionsContainer = suggestionsContainer;
    this.api = api;
    this.renderer = renderer;
    this.positioning = positioning;
    
    // Default configuration with override capability
    this.config = {
      debounceDelay: 300,            // Default debounce delay
      ...config                     // Allow overrides from settings
    };
    
    this.debounceTimer = null;
  }

  /**
   * Binds all necessary event listeners
   */
  bindEvents() {
    this.searchForm.addEventListener('submit', this.handleSubmit.bind(this));
    this.searchInput.addEventListener('input', this.handleInput.bind(this));
    document.addEventListener('click', this.handleDocumentClick.bind(this));
    
    if (this.suggestionsContainer) {
      this.suggestionsContainer.addEventListener('click', this.handleSuggestionClick.bind(this));
    }
  }

  /**
   * Handles clicks on suggestion links
   * @param {Event} event - Click event
   */
  handleSuggestionClick(event) {
    const targetLink = event.target.closest('a[href]');
    if (targetLink) {
      event.preventDefault();
      const url = targetLink.href;
      window.location.href = url;
    }
  }

  /**
   * Handles document clicks for clearing suggestions
   * @param {Event} event - Click event
   */
  handleDocumentClick(event) {
    if (event.button !== 0) return;
    
    if (
      this.searchForm?.contains(event.target) ||
      this.suggestionsContainer?.contains(event.target)
    ) return;
    
    this.renderer.clearSuggestions();
    this.positioning.cleanupResizeHandler();
  }

  /**
   * Handles form submission
   * @param {Event} event - Form submit event
   */
  handleSubmit(event) {
    event.preventDefault();
    const query = this.searchInput.value.trim();
    if (!query) return;
    
    const settings = this.api.settings;
    if (!settings.enable_search_engine) return;
    
    const standardSearchResultsUrl = `${window.location.origin}/search?q=${encodeURIComponent(query)}`;
    window.location.href = standardSearchResultsUrl;
  }

  /**
   * Handles input events for suggestions
   * @param {Event} event - Input event
   */
  handleInput(event) {
    const query = event.target.value.trim();
    clearTimeout(this.debounceTimer);
    this.renderer.hideDefaultSuggestions();
    
    if (query.length < this.config.minQueryLength) {
      this.renderer.clearSuggestions();
      this.positioning.cleanupResizeHandler();
      return;
    }
    
    this.debounceTimer = setTimeout(async () => {
      const suggestions = await this.api.fetchSuggestions(query, this.api.settings);
      const result = this.renderer.renderSuggestions(suggestions, query);
      
      if (result && result.showAllButton) {
        result.showAllButton.onclick = () => {
          if (query) {
            window.location.href = `/search?q=${encodeURIComponent(query)}`;
          }
        };
      }
      
      this.positioning.positionContainer();
      if (!this.positioning.resizeHandler) {
        this.positioning.setupResizeHandler();
      }
    }, this.config.debounceDelay);
  }
}

// --- IIFE to instantiate and run the application ---
(function () {
  console.log('[Search Enhancer] Script loaded.');
  
  async function initSearchEnhancer() {
    // 1. Find DOM elements
    const searchForm = document.querySelector('form[action*="/search"]');
    if (!searchForm) {
      console.error('[Search Enhancer] Search form not found.');
      return;
    }
    
    const searchInput = searchForm.querySelector('input[type="search"]') ||
      searchForm.querySelector('input[name="q"]');
    if (!searchInput) {
      console.error('[Search Enhancer] Search input not found within the form.');
      return;
    }
    
    // 2. Get the suggestions container
    let suggestionsContainer = document.getElementById('custom-search-suggestions-container');
    if (!suggestionsContainer) {
      // Create it if it doesn't exist
      suggestionsContainer = document.createElement('div');
      suggestionsContainer.id = 'custom-search-suggestions-container';
      suggestionsContainer.className = 'custom-search-suggestions';
      searchInput.parentElement.appendChild(suggestionsContainer);
    }
    
    // 3. Initialize the components with configuration
    const api = new SearchAPI('/apps/dinosearch', {
      apiPageSize: 10  // Can be overridden by settings
    });
    
    const renderer = new SearchRenderer(suggestionsContainer, {
      maxSuggestions: 5,
      minQueryLength: 2,
      language: 'de-DE',
      currency: 'EUR'
    });
    
    const positioning = new SearchPositioning(
      searchInput, 
      suggestionsContainer, 
      {
        gapBetweenSearchBarAndModal: 1,
        imageSize: 50
      }
    );
    
    const eventManager = new SearchEventManager(
      searchForm,
      searchInput,
      suggestionsContainer,
      api,
      renderer,
      positioning,
      {
        debounceDelay: 300
      }
    );
    
    // 4. Fetch settings
    const settings = await api.fetchSettings();
    if (!settings || !settings.enable_search_engine) {
      console.log('[Search Enhancer] Feature is disabled or settings fetch failed. Aborting.');
      return;
    }
    
    // Merge settings configuration with our defaults
    if (settings.search_engine_config) {
      // Update renderer config
      Object.assign(renderer.config, settings.search_engine_config);
      
      // Update positioning config
      Object.assign(positioning.config, settings.search_engine_config);
      
      // Update API config
      Object.assign(api.config, settings.search_engine_config);
      
      // Update event manager config
      Object.assign(eventManager.config, settings.search_engine_config);
    }
    
    // 5. Initialize event manager
    eventManager.bindEvents();
    
    console.log('[Search Enhancer] Initialization complete.');
  }
  
  function run() {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initSearchEnhancer);
    } else {
      initSearchEnhancer();
    }
  }
  
  run();
})();